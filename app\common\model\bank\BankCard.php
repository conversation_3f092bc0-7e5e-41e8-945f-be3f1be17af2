<?php

namespace app\common\model\bank;

use app\common\model\BaseModel;
use app\common\service\FileService;

/**
 * 银行卡模型
 * Class BankCard
 * @package app\common\model\bank
 */
class BankCard extends BaseModel
{

    //处理银行卡图标路径
    public function getIconAttr($value)
    {
        return (!empty($value) && is_string($value)) ? FileService::getFileUrl(trim($value)) : '';
    }

    /**
     * @notes 根据ID获取银行卡信息
     * @param int $id
     * @return array|null
     */
    public static function getBankCardById($id)
    {
        return self::field('id,name,icon')
            ->where(['id' => $id, 'status' => 1])
            ->findOrEmpty()
            ->toArray();
    }
}
