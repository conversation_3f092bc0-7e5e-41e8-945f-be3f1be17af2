<?php

namespace app\common\model\bank;

use app\common\model\BaseModel;

/**
 * 用户支付宝模型
 * Class UserAlipay
 * @package app\common\model\bank
 */
class UserAlipay extends BaseModel
{

    /**
     * @notes 获取用户支付宝列表
     * @param int $userId
     * @return array
     */
    public static function getUserAlipayList($userId)
    {
        return self::field('id,user_id,real_name,alipay_account,create_time')
            ->where('user_id', $userId)
            ->order('id desc')
            ->select()
            ->toArray();
    }


}
