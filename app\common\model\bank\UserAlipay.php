<?php

namespace app\common\model\bank;

use app\common\model\BaseModel;

/**
 * 用户支付宝模型
 * Class UserAlipay
 * @package app\common\model\bank
 */
class UserAlipay extends BaseModel
{

    /**
     * @notes 获取用户支付宝列表
     * @param int $userId
     * @return array
     */
    public static function getUserAlipayList($userId)
    {
        return self::field('id,user_id,real_name,alipay_account,create_time')
            ->where('user_id', $userId)
            ->order('id desc')
            ->select()
            ->toArray();
    }

    /**
     * @notes 添加用户支付宝
     * @param array $data
     * @return UserAlipay
     */
    public static function addUserAlipay($data)
    {
        return self::create([
            'user_id' => $data['user_id'],
            'real_name' => $data['real_name'],
            'alipay_account' => $data['alipay_account'],
            'create_time' => time(),
            'update_time' => time()
        ]);
    }

    /**
     * @notes 检查用户是否已绑定该支付宝账号
     * @param int $userId
     * @param string $alipayAccount
     * @return bool
     */
    public static function checkAlipayExists($userId, $alipayAccount)
    {
        return self::where('user_id', $userId)
            ->where('alipay_account', $alipayAccount)
            ->count() > 0;
    }
}
