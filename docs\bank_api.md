# 银行卡相关API文档

## 1. 获取银行卡列表

**接口地址：** `GET /applent/withdrawal/get_bank_card_list`

**接口描述：** 获取系统支持的银行卡列表

**请求参数：** 无

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "id": 1,
            "name": "中国工商银行",
            "icon": "http://domain.com/static/images/bank/icbc.png",
            "sort": 1
        },
        {
            "id": 2,
            "name": "中国建设银行", 
            "icon": "http://domain.com/static/images/bank/ccb.png",
            "sort": 2
        }
    ],
    "show": 1,
    "count": 0
}
```

## 2. 绑定用户银行卡

**接口地址：** `POST /applent/withdrawal/bind_user_bank_card`

**接口描述：** 用户绑定银行卡

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| bank_id | int | 是 | 银行ID |
| cardholder_name | string | 是 | 持卡人姓名，最大50字符 |
| card_number | string | 是 | 银行卡号，16-19位数字 |
| branch_name | string | 是 | 开户支行，最大100字符 |
| mobile | string | 是 | 预留手机号 |

**请求示例：**
```json
{
    "bank_id": 1,
    "cardholder_name": "张三",
    "card_number": "6222021234567890123",
    "branch_name": "北京朝阳支行",
    "mobile": "***********"
}
```

**返回示例：**
```json
{
    "code": 1,
    "msg": "绑定成功",
    "data": [],
    "show": 1,
    "count": 0
}
```

## 3. 绑定用户支付宝

**接口地址：** `POST /applent/withdrawal/bind_user_alipay`

**接口描述：** 用户绑定支付宝账号

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| real_name | string | 是 | 真实姓名，最大50字符 |
| alipay_account | string | 是 | 支付宝账号（邮箱或手机号） |

**请求示例：**
```json
{
    "real_name": "张三",
    "alipay_account": "<EMAIL>"
}
```

**返回示例：**
```json
{
    "code": 1,
    "msg": "绑定成功",
    "data": [],
    "show": 1,
    "count": 0
}
```

## 4. 获取用户银行卡列表

**接口地址：** `GET /applent/withdrawal/get_user_bank_card_list`

**接口描述：** 获取当前用户绑定的银行卡列表

**请求参数：** 无

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "id": 1,
            "user_id": 123,
            "bank_id": 1,
            "cardholder_name": "张三",
            "card_number": "6222021234567890123",
            "card_number_hidden": "6222****0123",
            "branch_name": "北京朝阳支行",
            "mobile": "***********",
            "create_time": **********,
            "create_time_text": "2022-01-01 12:00:00",
            "bank_card": {
                "id": 1,
                "name": "中国工商银行",
                "icon": "http://domain.com/static/images/bank/icbc.png"
            }
        }
    ],
    "show": 1,
    "count": 0
}
```

## 5. 获取用户支付宝列表

**接口地址：** `GET /applent/withdrawal/get_user_alipay_list`

**接口描述：** 获取当前用户绑定的支付宝账号列表

**请求参数：** 无

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": [
        {
            "id": 1,
            "user_id": 123,
            "real_name": "张三",
            "alipay_account": "<EMAIL>",
            "alipay_account_hidden": "zha****@example.com",
            "create_time": **********,
            "create_time_text": "2022-01-01 12:00:00"
        }
    ],
    "show": 1,
    "count": 0
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |

## 常见错误信息

- "银行不存在" - 传入的bank_id无效
- "该银行卡已绑定" - 用户已绑定相同的银行卡号
- "该支付宝账号已绑定" - 用户已绑定相同的支付宝账号
- "银行卡号格式不正确，请输入16-19位数字" - 银行卡号格式验证失败
- "支付宝账号格式不正确，请输入邮箱或手机号" - 支付宝账号格式验证失败

## 注意事项

1. 所有接口都需要用户登录，会自动获取当前用户ID
2. 银行卡号和支付宝账号在返回时会进行脱敏处理
3. 支付宝账号支持邮箱和手机号两种格式
4. 银行卡号必须是16-19位纯数字
5. 同一用户不能绑定相同的银行卡号或支付宝账号
