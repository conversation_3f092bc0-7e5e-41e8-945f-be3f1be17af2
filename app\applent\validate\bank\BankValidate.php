<?php

namespace app\applent\validate\bank;

use app\common\validate\BaseValidate;

/**
 * 银行卡验证器
 * Class BankValidate
 * @package app\applent\validate\bank
 */
class BankValidate extends BaseValidate
{
    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'bank_id' => 'require|integer|gt:0',
        'cardholder_name' => 'require|max:50',
        'card_number' => 'require|max:30|regex:/^[0-9]{16,19}$/',
        'branch_name' => 'require|max:100',
        'mobile' => 'require|mobile',
        'real_name' => 'require|max:50',
        'alipay_account' => 'require|max:100|regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$|^1[3-9]\d{9}$/',
    ];

    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'bank_id' => '银行ID',
        'cardholder_name' => '持卡人姓名',
        'card_number' => '银行卡号',
        'branch_name' => '开户支行',
        'mobile' => '预留手机号',
        'real_name' => '真实姓名',
        'alipay_account' => '支付宝账号',
    ];

    /**
     * 错误信息
     * @var string[]
     */
    protected $message = [
        'card_number.regex' => '银行卡号格式不正确，请输入16-19位数字',
        'alipay_account.regex' => '支付宝账号格式不正确，请输入邮箱或手机号',
    ];

    /**
     * @notes 绑定银行卡场景
     * @return BankValidate
     */
    public function sceneBindBankCard()
    {
        return $this->only(['bank_id', 'cardholder_name', 'card_number', 'branch_name', 'mobile']);
    }

    /**
     * @notes 绑定支付宝场景
     * @return BankValidate
     */
    public function sceneBindAlipay()
    {
        return $this->only(['real_name', 'alipay_account']);
    }
}
