<?php

namespace app\applent\controller\withdrawal;

use app\applent\controller\BaseApiController;
use app\applent\logic\withdrawal\WithdrawalLogic;
use app\applent\validate\withdrawal\WithdrawalValidate;
use app\applent\logic\bank\BankLogic;
use app\applent\validate\bank\BankValidate;

/**
 * 提现控制器
 * Class WithdrawalController
 * @package app\applent\controller\withdrawal
 */
class WithdrawalController extends BaseApiController
{
    /**
     * @notes 获取提现金额配置列表
     * @return \think\response\Json
     */
    public function get_withdrawal_config_list()
    {
        $result = WithdrawalLogic::getWithdrawalConfigList();
        if (false === $result) {
            return $this->fail(WithdrawalLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 获取银行卡列表
     * @return \think\response\Json
     */
    public function get_bank_card_list()
    {
        $result = BankLogic::getBankCardList();
        return $this->success('获取成功', $result, 1, 0);
    }

    /**
     * @notes 绑定用户银行卡
     * @return \think\response\Json
     */
    public function bind_user_bank_card()
    {
        // 验证参数
        $params = (new BankValidate())->post()->goCheck('bindBankCard');

        // 添加用户ID
        $params['user_id'] = $this->userId;

        $result = BankLogic::bindUserBankCard($params);
        if (false === $result) {
            return $this->fail(BankLogic::getError());
        }

        return $this->success($result == 1 ? '更新成功' : '绑定成功', [], 1, 1);
    }

    /**
     * @notes 绑定用户支付宝
     * @return \think\response\Json
     */
    public function bind_user_alipay()
    {
        // 验证参数
        $params = (new BankValidate())->post()->goCheck('bindAlipay');

        // 添加用户ID
        $params['user_id'] = $this->userId;

        $result = BankLogic::bindUserAlipay($params);
        if (false === $result) {
            return $this->fail(BankLogic::getError());
        }

        return $this->success($result == 1 ? '更新成功' : '绑定成功', [], 1, 1);
    }

    /**
     * @notes 获取用户银行卡和支付宝信息
     * @return \think\response\Json
     */
    public function get_user_payment_info()
    {
        $bankCardInfo = BankLogic::getUserBankCardInfo($this->userId);
        $alipayInfo = BankLogic::getUserAlipayInfo($this->userId);

        $result = [
            'bank_card' => $bankCardInfo ?: [],
            'alipay' => $alipayInfo ?: []
        ];

        return $this->success('获取成功', $result, 1, 0);
    }
}
