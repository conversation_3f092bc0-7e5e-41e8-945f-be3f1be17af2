<?php

namespace app\applent\controller\withdrawal;

use app\applent\controller\BaseApiController;
use app\applent\logic\withdrawal\WithdrawalLogic;
use app\applent\validate\withdrawal\WithdrawalValidate;

/**
 * 提现控制器
 * Class WithdrawalController
 * @package app\applent\controller\withdrawal
 */
class WithdrawalController extends BaseApiController
{
    /**
     * @notes 获取提现金额配置列表
     * @return \think\response\Json
     */
    public function get_withdrawal_config_list()
    {
        $result = WithdrawalLogic::getWithdrawalConfigList();
        if (false === $result) {
            return $this->fail(WithdrawalLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }
}
