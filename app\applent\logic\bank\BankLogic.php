<?php

namespace app\applent\logic\bank;

use app\common\logic\BaseLogic;
use app\common\model\bank\BankCard;
use app\common\model\bank\UserBankCard;
use app\common\model\bank\UserAlipay;
use app\common\service\FileService;

/**
 * 银行卡逻辑
 * Class BankLogic
 * @package app\applent\logic\bank
 */
class BankLogic extends BaseLogic
{
    /**
     * @notes 获取银行卡列表
     * @return array
     */
    public static function getBankCardList()
    {
        return BankCard::field('id,name,icon,sort')
            ->order('sort desc, id asc')
            ->where('status', 1)
            ->select()
            ->toArray();
    }

    /**
     * @notes 绑定用户银行卡
     * @param array $params
     * @return bool
     */
    public static function bindUserBankCard($params)
    {
        try {
            // 检查银行是否存在
            $bankInfo = BankCard::getBankCardById($params['bank_id']);
            if (empty($bankInfo)) {
                self::setError('银行不存在');
                return false;
            }

            // 检查用户是否已有银行卡记录
            $existCard = UserBankCard::where('user_id', $params['user_id'])->find();

            if ($existCard) {
                // 编辑现有银行卡
                $existCard->bank_id         = $params['bank_id'];
                $existCard->cardholder_name = $params['cardholder_name'];
                $existCard->card_number     = $params['card_number'];
                $existCard->branch_name     = $params['branch_name'];
                $existCard->mobile          = $params['mobile'];
                $existCard->update_time     = time();
                $existCard->save();

                $type = 1;
            } else {
                // 添加新银行卡
                UserBankCard::create([
                    'user_id'         => $params['user_id'],
                    'bank_id'         => $params['bank_id'],
                    'cardholder_name' => $params['cardholder_name'],
                    'card_number'     => $params['card_number'],
                    'branch_name'     => $params['branch_name'],
                    'mobile'          => $params['mobile'],
                    'create_time'     => time(),
                    'update_time'     => time()
                ]);

                $type = 2;
            }

            return $type;
        } catch (\Exception $e) {
            self::setError('绑定失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 绑定用户支付宝
     * @param array $params
     * @return bool
     */
    public static function bindUserAlipay($params)
    {
        try {
            // 检查用户是否已有支付宝记录
            $existAlipay = UserAlipay::where('user_id', $params['user_id'])->find();

            if ($existAlipay) {
                // 编辑现有支付宝账号
                $existAlipay->real_name         = $params['real_name'];
                $existAlipay->alipay_account    = $params['alipay_account'];
                $existAlipay->update_time       = time();
                $existAlipay->save();

                $type = 1;
            } else {
                // 添加新支付宝账号
                UserAlipay::create([
                    'user_id'        => $params['user_id'],
                    'real_name'      => $params['real_name'],
                    'alipay_account' => $params['alipay_account'],
                    'create_time'    => time(),
                    'update_time'    => time()
                ]);

                $type = 2;
            }

            return $type;
        } catch (\Exception $e) {
            self::setError('绑定失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取用户银行卡列表
     * @param int $userId
     * @return array
     */
    public static function getUserBankCardList($userId)
    {
        $list = UserBankCard::getUserBankCardList($userId);
        
        // 处理数据格式
        foreach ($list as &$item) {
            // 隐藏银行卡号中间部分
            $item['card_number_hidden'] = substr($item['card_number'], 0, 4) . '****' . substr($item['card_number'], -4);
            // 处理银行图标
            if (!empty($item['bank_card']['icon'])) {
                $item['bank_card']['icon'] = FileService::getFileUrl($item['bank_card']['icon']);
            }
            // 格式化时间
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        
        return $list;
    }

    /**
     * @notes 获取用户支付宝列表
     * @param int $userId
     * @return array
     */
    public static function getUserAlipayList($userId)
    {
        $list = UserAlipay::getUserAlipayList($userId);
        
        // 处理数据格式
        foreach ($list as &$item) {
            // 隐藏支付宝账号中间部分
            if (strpos($item['alipay_account'], '@') !== false) {
                // 邮箱格式
                $parts = explode('@', $item['alipay_account']);
                $item['alipay_account_hidden'] = substr($parts[0], 0, 3) . '****@' . $parts[1];
            } else {
                // 手机号格式
                $item['alipay_account_hidden'] = substr($item['alipay_account'], 0, 3) . '****' . substr($item['alipay_account'], -4);
            }
            // 格式化时间
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        
        return $list;
    }
}
