<?php

namespace app\applent\validate\withdrawal;

use app\common\validate\BaseValidate;

/**
 * 提现验证器
 * Class WithdrawalValidate
 * @package app\applent\validate\withdrawal
 */
class WithdrawalValidate extends BaseValidate
{
    protected $rule = [
        'config_id' => 'require|integer|gt:0',
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:1,100',
    ];

    protected $message = [
        'config_id.require' => '请选择提现金额',
        'config_id.integer' => '提现配置ID必须为整数',
        'config_id.gt' => '提现配置ID必须大于0',
        'page.integer' => '页码必须为整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须为整数',
        'limit.between' => '每页数量必须在1-100之间',
    ];

    /**
     * @notes 申请提现场景
     * @return WithdrawalValidate
     */
    public function sceneApplyWithdrawal()
    {
        return $this->only(['config_id']);
    }

    /**
     * @notes 提现记录列表场景
     * @return WithdrawalValidate
     */
    public function sceneWithdrawalList()
    {
        return $this->only(['page', 'limit']);
    }
}
