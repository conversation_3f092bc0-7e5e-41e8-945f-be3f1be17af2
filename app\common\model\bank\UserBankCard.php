<?php

namespace app\common\model\bank;

use app\common\model\BaseModel;

/**
 * 用户银行卡模型
 * Class UserBankCard
 * @package app\common\model\bank
 */
class UserBankCard extends BaseModel
{

    /**
     * @notes 关联银行卡表
     * @return \think\model\relation\BelongsTo
     */
    public function bankCard()
    {
        return $this->belongsTo(BankCard::class, 'bank_id', 'id');
    }

    /**
     * @notes 获取用户银行卡列表
     * @param int $userId
     * @return array
     */
    public static function getUserBankCardList($userId)
    {
        return self::with(['bankCard' => function($query) {
                $query->field('id,name,icon');
            }])
            ->field('id,user_id,bank_id,cardholder_name,card_number,branch_name,mobile,create_time')
            ->where('user_id', $userId)
            ->order('id desc')
            ->select()
            ->toArray();
    }

    /**
     * @notes 添加用户银行卡
     * @param array $data
     * @return UserBankCard
     */
    public static function addUserBankCard($data)
    {
        return self::create([
            'user_id' => $data['user_id'],
            'bank_id' => $data['bank_id'],
            'cardholder_name' => $data['cardholder_name'],
            'card_number' => $data['card_number'],
            'branch_name' => $data['branch_name'],
            'mobile' => $data['mobile'],
            'create_time' => time(),
            'update_time' => time()
        ]);
    }

    /**
     * @notes 检查用户是否已绑定该银行卡
     * @param int $userId
     * @param string $cardNumber
     * @return bool
     */
    public static function checkCardExists($userId, $cardNumber)
    {
        return self::where('user_id', $userId)
            ->where('card_number', $cardNumber)
            ->count() > 0;
    }
}
