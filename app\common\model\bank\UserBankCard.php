<?php

namespace app\common\model\bank;

use app\common\model\BaseModel;

/**
 * 用户银行卡模型
 * Class UserBankCard
 * @package app\common\model\bank
 */
class UserBankCard extends BaseModel
{

    /**
     * @notes 关联银行卡表
     * @return \think\model\relation\BelongsTo
     */
    public function bankCard()
    {
        return $this->belongsTo(BankCard::class, 'bank_id', 'id');
    }

    /**
     * @notes 获取用户银行卡列表
     * @param int $userId
     * @return array
     */
    public static function getUserBankCardList($userId)
    {
        return self::with(['bankCard' => function($query) {
                $query->field('id,name,icon');
            }])
            ->field('id,user_id,bank_id,cardholder_name,card_number,branch_name,mobile,create_time')
            ->where('user_id', $userId)
            ->order('id desc')
            ->select()
            ->toArray();
    }


}
