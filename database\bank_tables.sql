-- 银行卡表
CREATE TABLE `la_bank_card` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '银行卡名称',
  `icon` varchar(255) NOT NULL DEFAULT '' COMMENT '银行图标',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序字段，数值越大越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行卡表';

-- 用户银行卡表
CREATE TABLE `la_user_bank_card` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `bank_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属银行ID，关联la_bank_card表',
  `cardholder_name` varchar(50) NOT NULL DEFAULT '' COMMENT '持卡人姓名',
  `card_number` varchar(30) NOT NULL DEFAULT '' COMMENT '银行卡号',
  `branch_name` varchar(100) NOT NULL DEFAULT '' COMMENT '开户支行',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '预留手机号',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_bank_id` (`bank_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户银行卡表';

-- 用户支付宝表
CREATE TABLE `la_user_alipay` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `real_name` varchar(50) NOT NULL DEFAULT '' COMMENT '用户真实姓名',
  `alipay_account` varchar(100) NOT NULL DEFAULT '' COMMENT '支付宝账号',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户支付宝表';

-- 插入一些常用银行数据示例
INSERT INTO `la_bank_card` (`name`, `icon`, `sort`, `status`, `create_time`, `update_time`) VALUES
('中国工商银行', '/static/images/bank/icbc.png', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中国建设银行', '/static/images/bank/ccb.png', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中国农业银行', '/static/images/bank/abc.png', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中国银行', '/static/images/bank/boc.png', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('招商银行', '/static/images/bank/cmb.png', 5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('交通银行', '/static/images/bank/bcm.png', 6, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中信银行', '/static/images/bank/citic.png', 7, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('光大银行', '/static/images/bank/ceb.png', 8, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('华夏银行', '/static/images/bank/hxb.png', 9, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('民生银行', '/static/images/bank/cmbc.png', 10, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
