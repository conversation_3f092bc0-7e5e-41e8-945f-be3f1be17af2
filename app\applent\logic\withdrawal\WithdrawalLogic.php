<?php

namespace app\applent\logic\withdrawal;

use app\common\logic\BaseLogic;
use app\common\model\withdrawal\WithdrawalConfig;
use app\common\service\ConfigService;

/**
 * 提现逻辑
 * Class WithdrawalLogic
 * @package app\applent\logic\withdrawal
 */
class WithdrawalLogic extends BaseLogic
{
    /**
     * @notes 获取提现金额配置列表
     * @return array|false
     */
    public static function getWithdrawalConfigList()
    {
        $data = WithdrawalConfig::field('id,money,exchange_num,sort')
            ->order('sort asc, id asc')
            ->select()
            ->toArray();
        foreach ($data as $key => $value) {
            $data[$key]['money_text'] = number_format($value['money'], 2) . '元';
            $data[$key]['exchange_text'] = $value['exchange_num'] .  ConfigService::get('systemconfig', 'profit_name', 0);
        }
        return $data;
    }
}
